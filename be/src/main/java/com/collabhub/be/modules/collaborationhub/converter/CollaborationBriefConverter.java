package com.collabhub.be.modules.collaborationhub.converter;

import com.collabhub.be.modules.collaborationhub.dto.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.CollaborationBrief;
import org.jooq.generated.enums.BriefScope;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * Converter class for mapping between CollaborationBrief DTOs and jOOQ POJOs.
 * Handles conversion between different representations of collaboration brief data.
 */
@Component
public class CollaborationBriefConverter {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationBriefConverter.class);
    private final ObjectMapper objectMapper;

    public CollaborationBriefConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Converts a create request to a jOOQ POJO for database insertion.
     * Note: Scope functionality is disabled - all briefs are visible to all participants.
     *
     * @param request the create request
     * @param hubId the collaboration hub ID
     * @param createdByParticipantId the participant ID who created the brief
     * @return the jOOQ POJO
     */
    public CollaborationBrief toEntity(CollaborationBriefCreateRequest request, Long hubId, Long createdByParticipantId) {
        CollaborationBrief brief = new CollaborationBrief();
        brief.setHubId(hubId);
        brief.setTitle(request.getTitle());
        brief.setBody(request.getBody());
        brief.setCreatedByParticipantId(createdByParticipantId);

        // Always set to ALL_PARTICIPANTS scope (scope functionality disabled)
        brief.setScope(org.jooq.generated.enums.BriefScope.all_participants);
        brief.setSpecificParticipantIds(JSONB.valueOf("[]"));

        brief.setCreatedAt(LocalDateTime.now());
        brief.setUpdatedAt(LocalDateTime.now());
        return brief;
    }

    /**
     * Updates an existing jOOQ POJO with data from an update request.
     * Note: Scope functionality is disabled - all briefs are visible to all participants.
     *
     * @param brief the existing brief entity
     * @param request the update request
     * @return the updated jOOQ POJO
     */
    public CollaborationBrief updateEntity(CollaborationBrief brief, CollaborationBriefUpdateRequest request) {
        brief.setTitle(request.getTitle());
        brief.setBody(request.getBody());

        // Always set to ALL_PARTICIPANTS scope (scope functionality disabled)
        brief.setScope(org.jooq.generated.enums.BriefScope.all_participants);
        brief.setSpecificParticipantIds(JSONB.valueOf("[]"));

        brief.setUpdatedAt(LocalDateTime.now());
        return brief;
    }

    /**
     * Converts a jOOQ POJO to a response DTO.
     *
     * @param brief the jOOQ POJO
     * @param createdByParticipantName the name of the participant who created the brief
     * @return the response DTO
     */
    public CollaborationBriefResponse toResponse(CollaborationBrief brief, String createdByParticipantName) {
        BriefScopeDto scope = convertFromJooqBriefScope(brief.getScope());
        List<Long> specificParticipantIds = convertJsonbToParticipantIds(brief.getSpecificParticipantIds());

        return new CollaborationBriefResponse(
                brief.getId(),
                brief.getHubId(),
                brief.getTitle(),
                brief.getBody(),
                brief.getCreatedByParticipantId(),
                createdByParticipantName,
                scope,
                specificParticipantIds,
                brief.getCreatedAt(),
                brief.getUpdatedAt()
        );
    }

    /**
     * Converts a jOOQ POJO to a lightweight list item DTO.
     *
     * @param brief the jOOQ POJO
     * @param createdByParticipantName the name of the participant who created the brief
     * @return the list item DTO
     */
    public CollaborationBriefListItemDto toListItem(CollaborationBrief brief, String createdByParticipantName) {
        String bodyPreview = brief.getBody() != null && brief.getBody().length() > 200
                ? brief.getBody().substring(0, 200) + "..."
                : brief.getBody();

        BriefScopeDto scope = convertFromJooqBriefScope(brief.getScope());
        List<Long> specificParticipantIds = convertJsonbToParticipantIds(brief.getSpecificParticipantIds());

        return new CollaborationBriefListItemDto(
                brief.getId(),
                brief.getTitle(),
                bodyPreview,
                brief.getCreatedByParticipantId(),
                createdByParticipantName,
                scope,
                specificParticipantIds,
                brief.getCreatedAt(),
                brief.getUpdatedAt()
        );
    }

    /**
     * Converts a list of participant IDs to JSONB for database storage.
     *
     * @param participantIds the participant IDs list
     * @return the JSONB representation
     */
    private JSONB convertParticipantIdsToJsonb(List<Long> participantIds) {
        if (participantIds == null || participantIds.isEmpty()) {
            return JSONB.valueOf("[]");
        }

        try {
            String json = objectMapper.writeValueAsString(participantIds);
            return JSONB.valueOf(json);
        } catch (JsonProcessingException e) {
            logger.warn("Failed to convert participant IDs to JSON, using empty array: {}", e.getMessage());
            return JSONB.valueOf("[]");
        }
    }

    /**
     * Converts JSONB to a list of participant IDs.
     *
     * @param jsonb the JSONB representation
     * @return the participant IDs list
     */
    public List<Long> convertJsonbToParticipantIds(JSONB jsonb) {
        if (jsonb == null) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(jsonb.data(), new TypeReference<List<Long>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse participant IDs from JSON, returning empty list: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Converts DTO BriefScope to jOOQ BriefScope enum.
     *
     * @param scope the DTO scope
     * @return the jOOQ scope enum
     */
    private BriefScope convertToJooqBriefScope(BriefScopeDto scope) {
        if (scope == null) {
            return org.jooq.generated.enums.BriefScope.all_participants;
        }

        switch (scope) {
            case ALL_PARTICIPANTS:
                return org.jooq.generated.enums.BriefScope.all_participants;
            case ADMINS_REVIEWERS:
                return org.jooq.generated.enums.BriefScope.admins_reviewers;
            case ADMINS_ONLY:
                return org.jooq.generated.enums.BriefScope.admins_only;
            case CUSTOM_SELECTION:
                return org.jooq.generated.enums.BriefScope.custom_selection;
            default:
                return org.jooq.generated.enums.BriefScope.all_participants;
        }
    }

    /**
     * Converts jOOQ BriefScope enum to DTO BriefScope.
     *
     * @param jooqScope the jOOQ scope enum
     * @return the DTO scope
     */
    public BriefScopeDto convertFromJooqBriefScope(BriefScope jooqScope) {
        if (jooqScope == null) {
            return BriefScopeDto.ALL_PARTICIPANTS;
        }

        switch (jooqScope) {
            case all_participants:
                return BriefScopeDto.ALL_PARTICIPANTS;
            case admins_reviewers:
                return BriefScopeDto.ADMINS_REVIEWERS;
            case admins_only:
                return BriefScopeDto.ADMINS_ONLY;
            case custom_selection:
                return BriefScopeDto.CUSTOM_SELECTION;
            default:
                return BriefScopeDto.ALL_PARTICIPANTS;
        }
    }
}
