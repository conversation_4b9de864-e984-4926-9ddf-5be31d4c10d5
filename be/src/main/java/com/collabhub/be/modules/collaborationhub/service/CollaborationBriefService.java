package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.converter.CollaborationBriefConverter;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationBriefRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.jooq.Record;
import org.jooq.generated.tables.pojos.CollaborationBrief;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing collaboration briefs within collaboration hubs.
 * Provides business logic for brief operations with proper access control and validation.
 */
@Service
public class CollaborationBriefService {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationBriefService.class);

    // Error Messages
    private static final String BRIEF_NOT_FOUND_MESSAGE = "Brief not found with ID: ";
    private static final String BRIEF_TITLE_EXISTS_MESSAGE = "Brief with title '%s' already exists in this hub";
    private static final String USER_NOT_PARTICIPANT_MESSAGE = "User is not a participant in this collaboration hub";
    private static final String BRIEF_ACCESS_DENIED_MESSAGE = "Only brief creators and hub admins can %s briefs";
    private static final String UNKNOWN_CREATOR_NAME = "Unknown";

    // Log Messages
    private static final String CREATING_BRIEF_LOG = "Creating brief '{}' in hub {} for account {} by user {}";
    private static final String CREATED_BRIEF_LOG = "Created brief {} in hub {} for account {}";
    private static final String UPDATING_BRIEF_LOG = "Updating brief {} in hub {} for account {} by user {}";
    private static final String UPDATED_BRIEF_LOG = "Updated brief {} in hub {} for account {}";
    private static final String RETRIEVING_BRIEF_LOG = "Retrieving brief {} in hub {} for account {} by user {}";
    private static final String RETRIEVING_BRIEFS_LOG = "Retrieving briefs for hub {} with filter '{}', page {}, size {} for account {} by user {}";
    private static final String RETRIEVED_BRIEFS_LOG = "Retrieved {} briefs out of {} total for hub {} in account {}";
    private static final String DELETING_BRIEF_LOG = "Deleting brief {} in hub {} for account {} by user {}";
    private static final String DELETED_BRIEF_LOG = "Deleted brief {} in hub {} for account {}";
    private static final String BRIEF_CREATION_ALLOWED_LOG = "Brief creation allowed for participant {} with role {}";

    // Database Field Names
    private static final String CREATOR_DISPLAY_NAME_FIELD = "creator_display_name";
    private static final String CREATOR_EMAIL_FIELD = "creator_email";

    private final CollaborationBriefRepositoryImpl briefRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationBriefConverter briefConverter;
    private final CollabHubPermissionService collabHubPermissionService;

    public CollaborationBriefService(CollaborationBriefRepositoryImpl briefRepository,
                                   HubParticipantRepositoryImpl participantRepository,
                                   CollaborationBriefConverter briefConverter,
                                   CollabHubPermissionService collabHubPermissionService) {
        this.briefRepository = briefRepository;
        this.participantRepository = participantRepository;
        this.briefConverter = briefConverter;
        this.collabHubPermissionService = collabHubPermissionService;
    }

    /**
     * Creates a new collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param request the brief creation request
     * @param accountId the account ID for multi-tenancy
     * @param currentUserEmail the user email creating the brief
     * @return the created brief response
     */
    @Transactional
    public CollaborationBriefResponse createBrief(Long hubId, @Valid CollaborationBriefCreateRequest request,
                                                 Long accountId, String currentUserEmail) {
        logger.info(CREATING_BRIEF_LOG, request.getTitle(), hubId, accountId, currentUserEmail);

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, currentUserEmail);
        validateBriefCreationPermission(participant);
        validateScopeConfiguration(request.getScope(), request.getSpecificParticipantIds());

        CollaborationBrief brief = createBriefEntity(request, hubId, participant.getId());
        String creatorName = getParticipantDisplayName(participant);

        logger.info(CREATED_BRIEF_LOG, brief.getId(), hubId, accountId);

        return briefConverter.toResponse(brief, creatorName);
    }

    /**
     * Updates an existing collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param request the brief update request
     * @param accountId the account ID for multi-tenancy
     * @param currentUserEmail the user email updating the brief
     * @return the updated brief response
     */
    @Transactional
    public CollaborationBriefResponse updateBrief(Long hubId, Long briefId, @Valid CollaborationBriefUpdateRequest request,
                                                 Long accountId, String currentUserEmail) {
        logger.info(UPDATING_BRIEF_LOG, briefId, hubId, accountId, currentUserEmail);

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, currentUserEmail);
        CollaborationBrief brief = findBriefByIdAndHubId(briefId, hubId, accountId);
        validateBriefUpdatePermission(participant, brief);
        validateBriefTitleForUpdate(brief, request.getTitle(), hubId, accountId, briefId);
        validateScopeConfiguration(request.getScope(), request.getSpecificParticipantIds());

        CollaborationBrief updatedBrief = updateBriefEntity(brief, request);
        String creatorName = getCreatorNameForBrief(brief.getCreatedByParticipantId());

        logger.info(UPDATED_BRIEF_LOG, briefId, hubId, accountId);

        return briefConverter.toResponse(updatedBrief, creatorName);
    }

    /**
     * Retrieves a specific brief by ID.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param accountId the account ID for multi-tenancy
     * @param currentUserEmail the user email requesting the brief
     * @return the brief response
     */
    @Transactional(readOnly = true)
    public CollaborationBriefResponse getBriefById(Long hubId, Long briefId, Long accountId, String currentUserEmail) {
        logger.debug(RETRIEVING_BRIEF_LOG, briefId, hubId, accountId, currentUserEmail);

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, currentUserEmail);
        Record record = findBriefWithCreatorInfo(briefId, hubId);
        CollaborationBrief brief = record.into(CollaborationBrief.class);

        validateBriefAccess(brief, participant);
        String creatorName = extractCreatorName(record);

        return briefConverter.toResponse(brief, creatorName);
    }

    /**
     * Retrieves a paginated list of briefs for a collaboration hub.
     *
     * @param hubId the collaboration hub ID
     * @param pageRequest the pagination request
     * @param titleFilter optional title filter
     * @param accountId the account ID for multi-tenancy
     * @param currentUserEmail the user email requesting the briefs
     * @return paginated list of brief list items
     */
    @Transactional(readOnly = true)
    public PageResponse<CollaborationBriefListItemDto> getBriefs(Long hubId, PageRequest pageRequest,
                                                               String titleFilter, Long accountId, String currentUserEmail) {
        logger.debug(RETRIEVING_BRIEFS_LOG, hubId, titleFilter, pageRequest.getPage(), pageRequest.getSize(), accountId, currentUserEmail);

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, currentUserEmail);
        long totalCount = getBriefsCount(hubId, accountId, titleFilter);
        List<Record> records = getBriefsWithCreatorInfo(hubId, accountId, titleFilter, pageRequest.getOffset(), pageRequest.getSize());
        List<CollaborationBriefListItemDto> briefListItems = convertRecordsToBriefListItems(records, participant);

        logger.debug(RETRIEVED_BRIEFS_LOG, briefListItems.size(), totalCount, hubId, accountId);

        return PageResponse.of(briefListItems, pageRequest, totalCount);
    }

    /**
     * Deletes a collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param accountId the account ID for multi-tenancy
     * @param currentUserEmail the user email deleting the brief
     */
    @Transactional
    public void deleteBrief(Long hubId, Long briefId, Long accountId, String currentUserEmail) {
        logger.info(DELETING_BRIEF_LOG, briefId, hubId, accountId, currentUserEmail);

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, currentUserEmail);
        CollaborationBrief brief = findBriefByIdAndHubId(briefId, hubId, accountId);
        validateBriefDeletePermission(participant, brief);

        briefRepository.deleteById(briefId);

        logger.info(DELETED_BRIEF_LOG, briefId, hubId, accountId);
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    // ========================================
    // Validation Helper Methods
    // ========================================

    /**
     * Validates hub access and returns the participant for the user.
     */
    private HubParticipant validateHubAccessAndGetParticipant(Long hubId, String currentUserEmail) {
        // Use our new permission service to validate hub access
        collabHubPermissionService.validateCanViewHubContent(hubId);

        HubParticipant participant = participantRepository.findByHubIdAndEmail(hubId, currentUserEmail);
        if (participant == null || participant.getRemovedAt() != null) {
            throw new ForbiddenException(ErrorCode.HUB_ACCESS_DENIED, USER_NOT_PARTICIPANT_MESSAGE);
        }
        return participant;
    }

    /**
     * Validates that a participant can create briefs.
     */
    private void validateBriefCreationPermission(HubParticipant participant) {
        if (!accessControlService.canCreateBrief(participant)) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED, String.format(BRIEF_ACCESS_DENIED_MESSAGE, "create"));
        }
        logger.debug(BRIEF_CREATION_ALLOWED_LOG, participant.getId(), participant.getRole());
    }

    /**
     * Validates that a participant can update a specific brief.
     */
    private void validateBriefUpdatePermission(HubParticipant participant, CollaborationBrief brief) {
        if (!accessControlService.canEditBrief(participant, brief.getCreatedByParticipantId())) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED, String.format(BRIEF_ACCESS_DENIED_MESSAGE, "update"));
        }
    }

    /**
     * Validates that a participant can delete a specific brief.
     */
    private void validateBriefDeletePermission(HubParticipant participant, CollaborationBrief brief) {
        if (!accessControlService.canDeleteBrief(participant, brief.getCreatedByParticipantId())) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED, String.format(BRIEF_ACCESS_DENIED_MESSAGE, "delete"));
        }
    }

    /**
     * Validates scope configuration consistency.
     */
    private void validateScopeConfiguration(BriefScopeDto scope, List<Long> specificParticipantIds) {
        if (!accessControlService.isValidScopeConfiguration(scope, specificParticipantIds)) {
            throw new ConflictException(ErrorCode.INVALID_BRIEF_SCOPE, "Invalid scope configuration");
        }
    }

    /**
     * Validates brief access for the requesting participant.
     */
    private void validateBriefAccess(CollaborationBrief brief, HubParticipant participant) {
        BriefScopeDto scope = briefConverter.convertFromJooqBriefScope(brief.getScope());
        List<Long> specificParticipantIds = briefConverter.convertJsonbToParticipantIds(brief.getSpecificParticipantIds());

        if (!accessControlService.hasAccessToBrief(scope, specificParticipantIds, participant)) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED, "Access denied to this brief");
        }
    }

    // ========================================
    // Data Access Helper Methods
    // ========================================

    /**
     * Finds a brief by ID and hub ID.
     */
    private CollaborationBrief findBriefByIdAndHubId(Long briefId, Long hubId, Long accountId) {
        return briefRepository.findByIdAndHubIdAndAccountId(briefId, hubId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND, BRIEF_NOT_FOUND_MESSAGE + briefId));
    }

    /**
     * Finds a brief with creator information.
     */
    private Record findBriefWithCreatorInfo(Long briefId, Long hubId) {
        return briefRepository.findBriefWithCreatorInfo(briefId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND, BRIEF_NOT_FOUND_MESSAGE + briefId));
    }

    /**
     * Gets total count of briefs with filter.
     */
    private long getBriefsCount(Long hubId, Long accountId, String titleFilter) {
        return briefRepository.countBriefsWithFilter(hubId, accountId, titleFilter);
    }

    /**
     * Gets briefs with creator information.
     */
    private List<Record> getBriefsWithCreatorInfo(Long hubId, Long accountId, String titleFilter, int offset, int size) {
        return briefRepository.findBriefsWithCreatorInfo(hubId, accountId, titleFilter, offset, size);
    }

    /**
     * Gets creator name for a brief by participant ID.
     */
    private String getCreatorNameForBrief(Long createdByParticipantId) {
        HubParticipant creator = participantRepository.findById(createdByParticipantId);
        return creator != null ? getParticipantDisplayName(creator) : UNKNOWN_CREATOR_NAME;
    }

    // ========================================
    // Business Logic Helper Methods
    // ========================================

    /**
     * Creates and persists a brief entity.
     */
    private CollaborationBrief createBriefEntity(CollaborationBriefCreateRequest request, Long hubId, Long participantId) {
        CollaborationBrief brief = briefConverter.toEntity(request, hubId, participantId);
        briefRepository.insert(brief);
        return brief;
    }

    /**
     * Updates and persists a brief entity.
     */
    private CollaborationBrief updateBriefEntity(CollaborationBrief brief, CollaborationBriefUpdateRequest request) {
        CollaborationBrief updatedBrief = briefConverter.updateEntity(brief, request);
        briefRepository.update(updatedBrief);
        return updatedBrief;
    }

    /**
     * Converts records to brief list items with access control filtering.
     */
    private List<CollaborationBriefListItemDto> convertRecordsToBriefListItems(List<Record> records, HubParticipant participant) {
        return records.stream()
                .map(record -> {
                    CollaborationBrief brief = record.into(CollaborationBrief.class);

                    // Apply access control filtering
                    BriefScopeDto scope = briefConverter.convertFromJooqBriefScope(brief.getScope());
                    List<Long> specificParticipantIds = briefConverter.convertJsonbToParticipantIds(brief.getSpecificParticipantIds());

                    if (accessControlService.hasAccessToBrief(scope, specificParticipantIds, participant)) {
                        String creatorName = extractCreatorName(record);
                        return briefConverter.toListItem(brief, creatorName);
                    }
                    return null;
                })
                .filter(item -> item != null)
                .collect(Collectors.toList());
    }

    // ========================================
    // Utility Helper Methods
    // ========================================

    /**
     * Gets the display name for a participant.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        return participant.getEmail(); // Could be enhanced to include user names
    }

    /**
     * Extracts creator name from a joined record.
     */
    private String extractCreatorName(Record record) {
        String displayName = record.get(CREATOR_DISPLAY_NAME_FIELD, String.class);
        String creatorEmail = record.get(CREATOR_EMAIL_FIELD, String.class);

        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        } else {
            return creatorEmail != null ? creatorEmail : UNKNOWN_CREATOR_NAME;
        }
    }
}
